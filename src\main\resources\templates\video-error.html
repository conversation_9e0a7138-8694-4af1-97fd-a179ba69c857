<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>视频播放错误</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .error-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .error-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 500px;
            width: 90%;
            text-align: center;
        }
        .error-icon {
            font-size: 4rem;
            color: #dc3545;
            margin-bottom: 1rem;
        }
        .error-title {
            color: #333;
            margin-bottom: 1rem;
        }
        .error-message {
            color: #666;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        .solution-list {
            text-align: left;
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }
        .solution-list li {
            margin-bottom: 0.5rem;
        }
        .btn-group-custom {
            display: flex;
            gap: 0.5rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        @media (max-width: 576px) {
            .error-card {
                padding: 1.5rem;
            }
            .error-icon {
                font-size: 3rem;
            }
            .btn-group-custom {
                flex-direction: column;
            }
            .btn-group-custom .btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-card">
            <div class="error-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            
            <h2 class="error-title">视频播放失败</h2>
            
            <div class="error-message">
                <p th:if="${errorMessage}" th:text="${errorMessage}">抱歉，视频暂时无法播放。</p>
                <p th:unless="${errorMessage}">抱歉，视频暂时无法播放。</p>
            </div>
            
            <div class="solution-list">
                <h6><i class="fas fa-lightbulb me-2"></i>可能的解决方案：</h6>
                <ul>
                    <li><i class="fas fa-wifi me-2"></i>检查网络连接是否正常</li>
                    <li><i class="fas fa-sync-alt me-2"></i>刷新页面重新尝试</li>
                    <li><i class="fas fa-mobile-alt me-2"></i>尝试使用其他浏览器</li>
                    <li th:if="${isMobile}"><i class="fas fa-signal me-2"></i>在WiFi环境下尝试播放</li>
                    <li><i class="fas fa-clock me-2"></i>稍后再试</li>
                </ul>
            </div>
            
            <!-- 设备信息 -->
            <div class="device-info mb-3" th:if="${showDeviceInfo}">
                <small class="text-muted">
                    <strong>设备信息：</strong>
                    <span th:text="${isMobile ? '移动设备' : '桌面设备'}">设备类型</span>
                    <span th:if="${userAgent}" th:text="'(' + ${userAgent} + ')'"></span>
                </small>
            </div>
            
            <div class="btn-group-custom">
                <button class="btn btn-primary" onclick="location.reload()">
                    <i class="fas fa-redo me-2"></i>重新加载
                </button>
                
                <button class="btn btn-secondary" onclick="history.back()">
                    <i class="fas fa-arrow-left me-2"></i>返回上页
                </button>
                
                <a href="/" class="btn btn-outline-primary">
                    <i class="fas fa-home me-2"></i>返回首页
                </a>
                
                <button class="btn btn-info" onclick="showTechnicalInfo()">
                    <i class="fas fa-info-circle me-2"></i>技术信息
                </button>
            </div>
            
            <!-- 技术信息（默认隐藏） -->
            <div id="technical-info" class="mt-3" style="display: none;">
                <div class="alert alert-info text-start">
                    <h6><i class="fas fa-cog me-2"></i>技术详情</h6>
                    <div class="small">
                        <p><strong>视频URL:</strong> <span th:text="${videoUrl}">N/A</span></p>
                        <p><strong>错误代码:</strong> <span th:text="${errorCode}">N/A</span></p>
                        <p><strong>时间:</strong> <span id="error-time"></span></p>
                        <p><strong>浏览器:</strong> <span id="browser-info"></span></p>
                        <p><strong>支持格式:</strong> <span id="supported-formats"></span></p>
                    </div>
                    
                    <div class="mt-2">
                        <button class="btn btn-sm btn-outline-secondary" onclick="copyTechnicalInfo()">
                            <i class="fas fa-copy me-1"></i>复制信息
                        </button>
                        <a href="/mobile-test" class="btn btn-sm btn-outline-info">
                            <i class="fas fa-flask me-1"></i>兼容性测试
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 显示技术信息
        function showTechnicalInfo() {
            const techInfo = document.getElementById('technical-info');
            if (techInfo.style.display === 'none') {
                techInfo.style.display = 'block';
                
                // 填充技术信息
                document.getElementById('error-time').textContent = new Date().toLocaleString();
                document.getElementById('browser-info').textContent = navigator.userAgent;
                
                // 检测视频格式支持
                const video = document.createElement('video');
                const formats = {
                    MP4: video.canPlayType('video/mp4'),
                    WebM: video.canPlayType('video/webm'),
                    Ogg: video.canPlayType('video/ogg')
                };
                
                const supportedList = Object.entries(formats)
                    .filter(([format, support]) => support !== '')
                    .map(([format, support]) => `${format}(${support})`)
                    .join(', ');
                    
                document.getElementById('supported-formats').textContent = supportedList || '无';
            } else {
                techInfo.style.display = 'none';
            }
        }
        
        // 复制技术信息
        function copyTechnicalInfo() {
            const info = `
视频播放错误报告
================
时间: ${new Date().toLocaleString()}
URL: ${window.location.href}
视频URL: ${document.querySelector('[th\\:text="${videoUrl}"]')?.textContent || 'N/A'}
用户代理: ${navigator.userAgent}
屏幕尺寸: ${screen.width}x${screen.height}
网络状态: ${navigator.connection ? navigator.connection.effectiveType : '未知'}
            `.trim();
            
            if (navigator.clipboard) {
                navigator.clipboard.writeText(info).then(() => {
                    alert('技术信息已复制到剪贴板');
                });
            } else {
                // 兜底方案
                const textArea = document.createElement('textarea');
                textArea.value = info;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('技术信息已复制到剪贴板');
            }
        }
        
        // 自动重试机制
        let retryCount = 0;
        const maxRetries = 3;
        
        function autoRetry() {
            if (retryCount < maxRetries) {
                retryCount++;
                console.log(`自动重试第 ${retryCount} 次...`);
                
                setTimeout(() => {
                    // 检查网络状态
                    if (navigator.onLine) {
                        location.reload();
                    } else {
                        console.log('网络离线，跳过重试');
                    }
                }, 3000 * retryCount); // 递增延迟
            }
        }
        
        // 网络状态监听
        window.addEventListener('online', () => {
            console.log('网络已连接，尝试重新加载');
            location.reload();
        });
        
        window.addEventListener('offline', () => {
            console.log('网络已断开');
        });
        
        // 页面加载完成后的处理
        document.addEventListener('DOMContentLoaded', function() {
            // 如果是自动跳转到错误页面，可以尝试自动重试
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('auto') === 'true') {
                setTimeout(autoRetry, 2000);
            }
        });
    </script>
</body>
</html>
