<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="color-scheme" content="light only">
    <meta name="theme-color" content="#ffffff">
    <meta name="msapplication-navbutton-color" content="#ffffff">
    <meta name="apple-mobile-web-app-status-bar-style" content="light-content">
    <meta name="supported-color-schemes" content="light">
    <title th:text="${pageTitle}">视频播放</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">




    <!-- 自定义CSS -->
    <link href="/css/style.css" rel="stylesheet">
    <link href="/css/play-style.css" rel="stylesheet">
    <link href="/css/mobile-video.css" rel="stylesheet">




    </style>
</head>
<body style="background-color: #ffffff !important; color: #333333 !important; color-scheme: light only !important;">
    <!-- 导航栏 -->
    <nav class="navbar navbar-dark bg-primary sticky-top">
        <div class="container">
            <div class="d-flex w-100 align-items-center justify-content-between">
                <!-- 导航链接 -->
                <div class="d-flex align-items-center">
                    <ul class="navbar-nav d-flex flex-row mb-0">
                        <li class="nav-item">
                            <a class="nav-link px-2" href="/">
                                <i class="fas fa-home me-1"></i>首页
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link px-2" href="/videos">
                                <i class="fas fa-video me-1"></i>视频
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link px-2" href="/admin">
                                <i class="fas fa-cog me-1"></i>管理
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- 搜索框 -->
                <div class="search-container">
                    <form class="d-flex search-form" action="/search" method="get">
                        <input class="form-control me-2 search-input-mobile" type="search" name="keyword" placeholder="搜索...">
                        <button class="btn btn-outline-light btn-sm" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                        <button class="btn btn-outline-light btn-sm ms-1" type="button" onclick="toggleSearch()">
                            <i class="fas fa-times"></i>
                        </button>
                    </form>
                    <button class="btn btn-outline-light btn-sm search-toggle" onclick="toggleSearch()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container-fluid px-3 py-4">
        <div class="row justify-content-center">
            <!-- 视频播放区域 -->
            <div class="col-12 col-md-8 col-lg-6">
                <!-- 视频播放器容器 -->
                <div class="video-player-container mb-4">
                    <div class="video-wrapper">
                        <!-- 响应式视频播放器 - 移动端优化 -->
                        <video
                            id="video-player"
                            controls
                            preload="metadata"
                            playsinline
                            webkit-playsinline
                            x5-video-player-type="h5"
                            x5-video-player-fullscreen="true"
                            x5-video-orientation="portraint"
                            x5-playsinline="true"
                            crossorigin="anonymous"
                            muted="false"
                            th:data-video-url="${video.videoUrl}"
                            th:data-video-id="${video.id}"
                            th:poster="${video.thumbnailUrl}">
                            <!-- 多格式支持 - 按优先级排序 -->
                            <source th:src="${video.videoUrl}" type="video/mp4; codecs=&quot;avc1.42E01E, mp4a.40.2&quot;" th:if="${video.videoUrl != null and (video.videoUrl.contains('.mp4') or video.videoUrl.contains('aliyuncs.com'))}">
                            <source th:src="${video.videoUrl}" type="video/mp4" th:if="${video.videoUrl != null and (video.videoUrl.contains('.mp4') or video.videoUrl.contains('aliyuncs.com'))}">
                            <source th:src="${video.videoUrl}" type="video/webm; codecs=&quot;vp8, vorbis&quot;" th:if="${video.videoUrl != null and video.videoUrl.contains('.webm')}">
                            <source th:src="${video.videoUrl}" type="video/webm" th:if="${video.videoUrl != null and video.videoUrl.contains('.webm')}">
                            <source th:src="${video.videoUrl}" type="video/ogg; codecs=&quot;theora, vorbis&quot;" th:if="${video.videoUrl != null and video.videoUrl.contains('.ogg')}">
                            <!-- 兜底方案 -->
                            <source th:src="${video.videoUrl}" type="video/mp4" th:unless="${video.videoUrl == null}">

                            <!-- 不支持时的提示 -->
                            <div class="video-not-supported">
                                <div class="alert alert-warning text-center">
                                    <h5><i class="fas fa-exclamation-triangle"></i> 视频无法播放</h5>
                                    <p>您的浏览器不支持HTML5视频播放，请尝试：</p>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-mobile-alt"></i> 使用现代移动浏览器</li>
                                        <li><i class="fas fa-wifi"></i> 检查网络连接</li>
                                        <li><i class="fas fa-download"></i> <a th:href="${video.videoUrl}" target="_blank">直接下载视频</a></li>
                                    </ul>
                                </div>
                            </div>
                        </video>
                        <!-- 加载状态 -->
                        <div class="video-loading" id="video-loading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>








                    </div>
                </div>

                <!-- 视频信息 -->
                <div class="video-info bg-white rounded-3 shadow-sm p-4 mb-4">
                    <h1 class="video-title h4 mb-3" th:text="${video.title}">视频标题</h1>
                    <div class="video-stats">
                        <time class="video-date text-muted" th:text="${#temporals.format(video.createdTime, 'yyyy-MM-dd')}">2024-01-01</time>
                    </div>
                    <!-- 视频描述 -->
                    <div class="video-description mt-3" th:if="${video.description}">
                        <p class="text-muted mb-0" th:text="${video.description}">视频描述</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <!-- <h5><i class="fas fa-play-circle me-2"></i>佳茵轻康</h5> -->
                    <p class="mb-0">轻康自然，享瘦生活。</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">
                        <small>© 2025 加盟合作. <a href="/about" class="text-light text-decoration-none">联系我们&nbsp;&nbsp;&nbsp;</a></small>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 视频格式处理器 -->
    <script src="/js/video-format-handler.js"></script>
    <!-- 移动端视频播放器优化 -->
    <script src="/js/mobile-video-player.js"></script>


    <!-- 自定义JS -->
    <script src="/js/main.js"></script>


    <script th:inline="javascript">
        // 初始化响应式HTML5视频播放器 - 移动端优化
        document.addEventListener('DOMContentLoaded', function() {
            const videoElement = document.getElementById('video-player');
            const videoUrl = videoElement.dataset.videoUrl;
            const videoId = videoElement.dataset.videoId;
            const loadingElement = document.getElementById('video-loading');

            // 移动端检测
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

            // 初始化格式处理器
            const formatHandler = new VideoFormatHandler();
            const compatibilityReport = formatHandler.getCompatibilityReport();

            // 检查浏览器视频格式支持
            function checkVideoSupport() {
                const video = document.createElement('video');
                const formats = {
                    mp4: video.canPlayType('video/mp4; codecs="avc1.42E01E, mp4a.40.2"'),
                    webm: video.canPlayType('video/webm; codecs="vp8, vorbis"'),
                    ogg: video.canPlayType('video/ogg; codecs="theora, vorbis"')
                };

                console.log('浏览器视频格式支持:', formats);
                return formats;
            }

            // 设置视频源和移动端优化
            if (videoUrl) {
                // 检查格式支持
                const supportedFormats = checkVideoSupport();

                // 使用格式处理器创建多源支持
                const sources = formatHandler.createVideoSources(videoElement, videoUrl);
                console.log('创建的视频源:', sources);

                // 获取最佳格式URL作为主源
                const bestUrl = formatHandler.getBestFormat(videoUrl);
                videoElement.src = bestUrl;

                // 显示兼容性警告（如果需要）
                const container = videoElement.closest('.video-player-container');
                formatHandler.showCompatibilityWarning(container, compatibilityReport);

                // 移动端特殊处理
                if (isMobile) {
                    videoElement.setAttribute('webkit-playsinline', 'true');
                    videoElement.setAttribute('x5-video-player-type', 'h5');
                    videoElement.setAttribute('x5-video-player-fullscreen', 'true');
                    videoElement.setAttribute('x5-video-orientation', 'portraint');
                    videoElement.setAttribute('x5-playsinline', 'true');

                    // iOS特殊处理
                    if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
                        videoElement.muted = false;
                        videoElement.setAttribute('playsinline', 'true');
                    }

                    // Android特殊处理
                    if (/Android/.test(navigator.userAgent)) {
                        videoElement.setAttribute('webkit-playsinline', 'true');
                        // 微信浏览器特殊处理
                        if (/MicroMessenger/.test(navigator.userAgent)) {
                            videoElement.setAttribute('x5-video-player-type', 'h5-page');
                            videoElement.setAttribute('x5-video-orientation', 'landscape|portrait');
                        }
                    }
                }

                // 如果MP4格式不支持，尝试其他格式
                if (!supportedFormats.mp4 || supportedFormats.mp4 === '') {
                    console.warn('MP4格式可能不支持，尝试其他格式');
                    showFormatWarning();
                }
            }

            // 生成视频首帧缩略图
            function generateThumbnail() {
                if (!videoElement.poster) {
                    videoElement.addEventListener('loadeddata', function() {
                        try {
                            const canvas = document.createElement('canvas');
                            const ctx = canvas.getContext('2d');

                            // 设置画布尺寸
                            canvas.width = videoElement.videoWidth;
                            canvas.height = videoElement.videoHeight;

                            // 绘制视频首帧
                            ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);

                            // 转换为base64图片
                            const thumbnailDataUrl = canvas.toDataURL('image/jpeg', 0.8);
                            videoElement.poster = thumbnailDataUrl;

                            console.log('视频首帧缩略图生成成功');
                        } catch (error) {
                            console.warn('无法生成视频缩略图:', error);
                        }
                    });
                }
            }

            // 隐藏加载状态
            function hideLoading() {
                if (loadingElement) {
                    loadingElement.style.display = 'none';
                }
            }

            // 显示加载状态
            function showLoading() {
                if (loadingElement) {
                    loadingElement.style.display = 'flex';
                }
            }

            // 初始化加载状态
            showLoading();

            // 生成缩略图
            generateThumbnail();

            // 视频加载开始
            videoElement.addEventListener('loadstart', function() {
                console.log('开始加载视频');
                showLoading();
            });

            // 视频元数据加载完成
            videoElement.addEventListener('loadedmetadata', function() {
                console.log('视频元数据加载完成');
                hideLoading();
            });

            // 视频数据加载完成
            videoElement.addEventListener('loadeddata', function() {
                console.log('视频数据加载完成');
                hideLoading();
            });

            // 视频可以播放
            videoElement.addEventListener('canplay', function() {
                console.log('视频可以播放');
                hideLoading();
            });

            // 错误处理
            videoElement.addEventListener('error', function(e) {
                console.error('视频播放出错:', e);
                hideLoading();

                let errorMessage = '视频加载失败，请检查网络连接。';

                if (videoElement.error) {
                    console.error('错误代码:', videoElement.error.code);
                    console.error('错误信息:', videoElement.error.message);

                    switch (videoElement.error.code) {
                        case 1:
                            errorMessage = '视频加载被中止。';
                            break;
                        case 2:
                            errorMessage = '网络错误，无法加载视频。';
                            break;
                        case 3:
                            errorMessage = '视频解码失败或格式不支持。';
                            break;
                        case 4:
                            errorMessage = '视频不存在或无法访问。';
                            break;
                    }
                }

                // 显示错误信息
                const errorDiv = document.createElement('div');
                errorDiv.className = 'alert alert-danger mt-3';
                errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle me-2"></i>${errorMessage}`;
                videoElement.parentNode.appendChild(errorDiv);
            });

            // 播放开始事件
            videoElement.addEventListener('play', function() {
                console.log('视频开始播放，ID:', videoId);
            });

            // 视频暂停事件
            videoElement.addEventListener('pause', function() {
                console.log('视频已暂停，ID:', videoId);
            });

            // 视频结束事件
            videoElement.addEventListener('ended', function() {
                console.log('视频播放结束，ID:', videoId);
            });

            // 显示格式警告
            function showFormatWarning() {
                const warningDiv = document.createElement('div');
                warningDiv.className = 'alert alert-warning mt-2';
                warningDiv.innerHTML = `
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>视频格式兼容性提示</h6>
                    <p class="mb-2">检测到您的浏览器对当前视频格式支持有限，建议：</p>
                    <ul class="mb-2">
                        <li>使用Chrome、Safari等现代浏览器</li>
                        <li>更新浏览器到最新版本</li>
                        <li>在WiFi环境下尝试播放</li>
                    </ul>
                `;

                const container = videoElement.closest('.video-player-container');
                if (container && !container.querySelector('.alert-warning')) {
                    container.appendChild(warningDiv);
                }
            }

            // 显示网络状态
            function showNetworkStatus() {
                if ('connection' in navigator) {
                    const connection = navigator.connection;
                    const networkDiv = document.createElement('div');
                    networkDiv.className = 'network-status';
                    networkDiv.textContent = `网络: ${connection.effectiveType || 'unknown'}`;

                    if (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g') {
                        networkDiv.classList.add('slow');
                    } else if (connection.effectiveType === '4g') {
                        networkDiv.classList.add('fast');
                    }

                    const container = videoElement.closest('.video-wrapper');
                    if (container && !container.querySelector('.network-status')) {
                        container.appendChild(networkDiv);
                    }
                }
            }

            // 显示网络状态
            if (isMobile) {
                showNetworkStatus();
            }

            console.log('响应式HTML5视频播放器已准备就绪');
            console.log('设备信息:', {
                isMobile: isMobile,
                userAgent: navigator.userAgent,
                screen: `${screen.width}x${screen.height}`,
                connection: navigator.connection ? navigator.connection.effectiveType : 'unknown'
            });
        });














    </script>



    <!-- 简单的视频播放器初始化 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const videoElement = document.getElementById('video-player');
            const loadingElement = document.getElementById('video-loading');

            if (!videoElement) return;

            // 隐藏加载状态
            function hideLoading() {
                if (loadingElement) {
                    loadingElement.style.display = 'none';
                }
            }

            // 显示加载状态
            function showLoading() {
                if (loadingElement) {
                    loadingElement.style.display = 'flex';
                }
            }

            // 视频加载完成
            videoElement.addEventListener('loadeddata', function() {
                hideLoading();
                console.log('视频加载完成');
            });

            // 视频开始播放
            videoElement.addEventListener('play', function() {
                hideLoading();
                console.log('视频开始播放');
            });

            // 视频暂停
            videoElement.addEventListener('pause', function() {
                console.log('视频暂停');
            });

            // 视频结束
            videoElement.addEventListener('ended', function() {
                console.log('视频播放结束');
            });

            // 移动端检测和优化
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
            const isAndroid = /Android/.test(navigator.userAgent);

            // 移动端优化设置
            if (isMobile) {
                if (isIOS) {
                    videoElement.setAttribute('webkit-playsinline', 'true');
                    videoElement.setAttribute('playsinline', 'true');
                }
                if (isAndroid) {
                    videoElement.setAttribute('x5-video-player-type', 'h5');
                    videoElement.setAttribute('x5-video-player-fullscreen', 'true');
                }
                videoElement.preload = 'metadata';
            }

            // 增强的错误处理
            videoElement.addEventListener('error', function(e) {
                hideLoading();
                console.error('视频播放出错:', e);
                console.error('错误代码:', videoElement.error ? videoElement.error.code : 'unknown');

                let errorMessage = '视频加载失败';
                if (videoElement.error) {
                    switch(videoElement.error.code) {
                        case 1: errorMessage = '视频加载被中止'; break;
                        case 2: errorMessage = '网络错误导致视频下载失败'; break;
                        case 3: errorMessage = '视频解码失败'; break;
                        case 4: errorMessage = '视频格式不支持或文件损坏'; break;
                        default: errorMessage = '未知错误';
                    }
                }

                // 显示详细错误信息
                const errorDiv = document.createElement('div');
                errorDiv.className = 'alert alert-danger mt-3';
                errorDiv.innerHTML = `
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>${errorMessage}</h6>
                    <p class="mb-2">可能的解决方案：</p>
                    <ul class="mb-2">
                        <li>检查网络连接</li>
                        <li>尝试刷新页面</li>
                        ${isMobile ? '<li>尝试在WiFi环境下播放</li>' : ''}
                        <li>使用其他浏览器</li>
                    </ul>
                    <button class="btn btn-primary btn-sm me-2" onclick="location.reload()">
                        <i class="fas fa-redo"></i> 重新加载
                    </button>
                    <button class="btn btn-secondary btn-sm" onclick="history.back()">
                        <i class="fas fa-arrow-left"></i> 返回
                    </button>
                `;
                videoElement.parentNode.appendChild(errorDiv);
            });

            // 等待事件
            videoElement.addEventListener('waiting', function() {
                showLoading();
            });

            // 可以播放事件
            videoElement.addEventListener('canplay', function() {
                hideLoading();
            });

            // 移动端触摸优化
            if (isMobile) {
                let touchStartTime = 0;

                videoElement.addEventListener('touchstart', function(e) {
                    touchStartTime = Date.now();
                });

                videoElement.addEventListener('touchend', function(e) {
                    const touchDuration = Date.now() - touchStartTime;
                    // 短触摸（小于300ms）切换播放/暂停
                    if (touchDuration < 300) {
                        if (videoElement.paused) {
                            videoElement.play().catch(err => {
                                console.log('自动播放被阻止:', err);
                            });
                        } else {
                            videoElement.pause();
                        }
                    }
                });
            }

            console.log('增强的移动端视频播放器初始化完成');
            console.log('设备类型:', isMobile ? '移动设备' : '桌面设备');
            if (isMobile) {
                console.log('移动设备详情:', isIOS ? 'iOS' : isAndroid ? 'Android' : '其他');
            }
        });
    </script>
</body>
</html>

