package com.videoplayer.util;

import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * 视频URL处理工具类
 * 处理OSS URL优化、移动端适配等
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Component
public class VideoUrlUtil {

    /**
     * 优化视频URL，特别是阿里云OSS URL
     * 
     * @param originalUrl 原始视频URL
     * @param isMobile 是否为移动端
     * @return 优化后的URL
     */
    public static String optimizeVideoUrl(String originalUrl, boolean isMobile) {
        if (!StringUtils.hasText(originalUrl)) {
            return originalUrl;
        }

        // 检查是否为阿里云OSS URL
        if (originalUrl.contains("aliyuncs.com")) {
            return optimizeOSSUrl(originalUrl, isMobile);
        }

        // 其他云存储服务的优化可以在这里添加
        return originalUrl;
    }

    /**
     * 优化阿里云OSS视频URL
     * 
     * @param ossUrl OSS URL
     * @param isMobile 是否为移动端
     * @return 优化后的URL
     */
    private static String optimizeOSSUrl(String ossUrl, boolean isMobile) {
        try {
            String separator = ossUrl.contains("?") ? "&" : "?";
            
            if (isMobile) {
                // 移动端优化：添加适合移动设备的参数
                StringBuilder optimizedUrl = new StringBuilder(ossUrl);
                optimizedUrl.append(separator);
                
                // 添加移动端优化参数
                optimizedUrl.append("x-oss-process=video/snapshot,t_1000,f_jpg,w_600,h_400,m_fast");
                
                // 添加缓存控制
                optimizedUrl.append("&x-oss-cache-control=max-age=3600");
                
                return optimizedUrl.toString();
            } else {
                // 桌面端优化
                StringBuilder optimizedUrl = new StringBuilder(ossUrl);
                optimizedUrl.append(separator);
                
                // 添加桌面端优化参数
                optimizedUrl.append("x-oss-process=video/snapshot,t_1000,f_jpg,w_1200,h_800,m_fast");
                
                // 添加缓存控制
                optimizedUrl.append("&x-oss-cache-control=max-age=7200");
                
                return optimizedUrl.toString();
            }
        } catch (Exception e) {
            // 如果优化失败，返回原始URL
            return ossUrl;
        }
    }

    /**
     * 生成视频缩略图URL
     * 
     * @param videoUrl 视频URL
     * @param width 缩略图宽度
     * @param height 缩略图高度
     * @return 缩略图URL
     */
    public static String generateThumbnailUrl(String videoUrl, int width, int height) {
        if (!StringUtils.hasText(videoUrl)) {
            return null;
        }

        if (videoUrl.contains("aliyuncs.com")) {
            String separator = videoUrl.contains("?") ? "&" : "?";
            return videoUrl + separator + String.format("x-oss-process=video/snapshot,t_1000,f_jpg,w_%d,h_%d,m_fast", width, height);
        }

        return null;
    }

    /**
     * 检查视频URL是否有效
     * 
     * @param videoUrl 视频URL
     * @return 是否有效
     */
    public static boolean isValidVideoUrl(String videoUrl) {
        if (!StringUtils.hasText(videoUrl)) {
            return false;
        }

        // 检查URL格式
        if (!videoUrl.startsWith("http://") && !videoUrl.startsWith("https://")) {
            return false;
        }

        // 检查视频文件扩展名
        String lowerUrl = videoUrl.toLowerCase();
        return lowerUrl.contains(".mp4") || 
               lowerUrl.contains(".webm") || 
               lowerUrl.contains(".ogg") || 
               lowerUrl.contains(".avi") || 
               lowerUrl.contains(".mov") ||
               lowerUrl.contains("aliyuncs.com"); // OSS可能没有扩展名
    }

    /**
     * 获取视频格式
     * 
     * @param videoUrl 视频URL
     * @return 视频格式
     */
    public static String getVideoFormat(String videoUrl) {
        if (!StringUtils.hasText(videoUrl)) {
            return "unknown";
        }

        String lowerUrl = videoUrl.toLowerCase();
        if (lowerUrl.contains(".mp4")) return "mp4";
        if (lowerUrl.contains(".webm")) return "webm";
        if (lowerUrl.contains(".ogg")) return "ogg";
        if (lowerUrl.contains(".avi")) return "avi";
        if (lowerUrl.contains(".mov")) return "mov";
        
        // 对于OSS等云存储，默认返回mp4
        if (lowerUrl.contains("aliyuncs.com")) return "mp4";
        
        return "unknown";
    }

    /**
     * 添加防盗链参数（如果需要）
     * 
     * @param videoUrl 视频URL
     * @param referer 来源域名
     * @return 添加防盗链参数后的URL
     */
    public static String addRefererProtection(String videoUrl, String referer) {
        if (!StringUtils.hasText(videoUrl) || !StringUtils.hasText(referer)) {
            return videoUrl;
        }

        // 这里可以根据具体的CDN服务商添加防盗链参数
        // 例如阿里云OSS的防盗链配置
        
        return videoUrl;
    }

    /**
     * 检测用户代理是否为移动设备
     * 
     * @param userAgent 用户代理字符串
     * @return 是否为移动设备
     */
    public static boolean isMobileDevice(String userAgent) {
        if (!StringUtils.hasText(userAgent)) {
            return false;
        }

        String lowerUserAgent = userAgent.toLowerCase();
        return lowerUserAgent.contains("android") ||
               lowerUserAgent.contains("iphone") ||
               lowerUserAgent.contains("ipad") ||
               lowerUserAgent.contains("ipod") ||
               lowerUserAgent.contains("blackberry") ||
               lowerUserAgent.contains("windows phone") ||
               lowerUserAgent.contains("mobile");
    }
}
