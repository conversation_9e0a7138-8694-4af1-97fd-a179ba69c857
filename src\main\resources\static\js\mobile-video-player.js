/**
 * 移动端视频播放器优化脚本
 * Mobile Video Player Optimization
 * <AUTHOR>
 * @version 1.0.0
 */

class MobileVideoPlayer {
    constructor(videoElement) {
        this.video = videoElement;
        this.isMobile = this.detectMobile();
        this.isIOS = this.detectIOS();
        this.isAndroid = this.detectAndroid();
        this.init();
    }

    /**
     * 检测是否为移动设备
     */
    detectMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    /**
     * 检测是否为iOS设备
     */
    detectIOS() {
        return /iPad|iPhone|iPod/.test(navigator.userAgent);
    }

    /**
     * 检测是否为Android设备
     */
    detectAndroid() {
        return /Android/.test(navigator.userAgent);
    }

    /**
     * 初始化移动端优化
     */
    init() {
        if (!this.isMobile) return;

        this.setupMobileAttributes();
        this.setupTouchEvents();
        this.setupOrientationChange();
        this.setupNetworkOptimization();
        
        console.log('移动端视频播放器优化已启用');
        console.log('设备信息:', {
            isMobile: this.isMobile,
            isIOS: this.isIOS,
            isAndroid: this.isAndroid,
            userAgent: navigator.userAgent
        });
    }

    /**
     * 设置移动端属性
     */
    setupMobileAttributes() {
        // 通用移动端属性
        this.video.setAttribute('playsinline', 'true');
        this.video.setAttribute('webkit-playsinline', 'true');
        this.video.setAttribute('crossorigin', 'anonymous');

        // iOS特殊属性
        if (this.isIOS) {
            this.video.setAttribute('webkit-playsinline', 'true');
            this.video.muted = false; // iOS允许声音

            // iOS版本检测
            const iosVersion = this.getIOSVersion();
            if (iosVersion && iosVersion < 10) {
                // iOS 10以下版本的特殊处理
                this.video.setAttribute('controls', 'true');
                this.video.preload = 'none';
            }
        }

        // Android特殊属性
        if (this.isAndroid) {
            this.video.setAttribute('x5-video-player-type', 'h5');
            this.video.setAttribute('x5-video-player-fullscreen', 'true');
            this.video.setAttribute('x5-video-orientation', 'portraint');
            this.video.setAttribute('x5-playsinline', 'true');

            // 微信浏览器特殊处理
            if (this.isWeChat()) {
                this.video.setAttribute('x5-video-player-type', 'h5-page');
                this.video.setAttribute('x5-video-orientation', 'landscape|portrait');
                this.video.setAttribute('x5-video-ignore-metadata', 'true');
            }

            // QQ浏览器特殊处理
            if (this.isQQBrowser()) {
                this.video.setAttribute('x5-video-player-type', 'h5');
                this.video.setAttribute('x5-video-player-fullscreen', 'true');
            }
        }

        // 预加载策略
        this.video.preload = 'metadata';

        // 添加错误处理
        this.setupErrorHandling();
    }

    /**
     * 获取iOS版本
     */
    getIOSVersion() {
        const match = navigator.userAgent.match(/OS (\d+)_(\d+)_?(\d+)?/);
        if (match) {
            return parseInt(match[1], 10);
        }
        return null;
    }

    /**
     * 检测是否为微信浏览器
     */
    isWeChat() {
        return /MicroMessenger/i.test(navigator.userAgent);
    }

    /**
     * 检测是否为QQ浏览器
     */
    isQQBrowser() {
        return /QQBrowser/i.test(navigator.userAgent);
    }

    /**
     * 设置错误处理
     */
    setupErrorHandling() {
        this.video.addEventListener('error', (e) => {
            console.error('视频播放错误:', e);
            this.handleVideoError();
        });

        this.video.addEventListener('stalled', () => {
            console.warn('视频加载停滞');
            this.showLoadingIndicator();
        });

        this.video.addEventListener('waiting', () => {
            console.log('视频缓冲中');
            this.showLoadingIndicator();
        });

        this.video.addEventListener('canplay', () => {
            this.hideLoadingIndicator();
        });
    }

    /**
     * 处理视频错误
     */
    handleVideoError() {
        const error = this.video.error;
        let errorMessage = '视频播放失败';
        let suggestions = [];

        if (error) {
            switch (error.code) {
                case 1:
                    errorMessage = '视频加载被中止';
                    suggestions = ['检查网络连接', '重新加载页面'];
                    break;
                case 2:
                    errorMessage = '网络错误';
                    suggestions = ['检查网络连接', '尝试使用WiFi', '稍后重试'];
                    break;
                case 3:
                    errorMessage = '视频解码失败';
                    suggestions = ['更新浏览器', '尝试其他浏览器', '检查视频格式'];
                    break;
                case 4:
                    errorMessage = '视频格式不支持';
                    suggestions = ['使用现代浏览器', '更新浏览器版本', '联系技术支持'];
                    break;
            }
        }

        this.showErrorMessage(errorMessage, suggestions);
    }

    /**
     * 显示错误消息
     */
    showErrorMessage(message, suggestions = []) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'mobile-error-message';
        errorDiv.innerHTML = `
            <div class="alert alert-danger">
                <h6><i class="fas fa-exclamation-triangle"></i> ${message}</h6>
                ${suggestions.length > 0 ? `
                    <p class="mb-2">建议解决方案：</p>
                    <ul class="mb-2">
                        ${suggestions.map(s => `<li>${s}</li>`).join('')}
                    </ul>
                ` : ''}
                <div class="mt-2">
                    <button class="btn btn-primary btn-sm me-2" onclick="location.reload()">
                        <i class="fas fa-redo"></i> 重新加载
                    </button>
                    <button class="btn btn-secondary btn-sm" onclick="history.back()">
                        <i class="fas fa-arrow-left"></i> 返回
                    </button>
                </div>
            </div>
        `;

        const container = this.video.parentElement;
        if (container && !container.querySelector('.mobile-error-message')) {
            container.appendChild(errorDiv);
        }
    }

    /**
     * 显示加载指示器
     */
    showLoadingIndicator() {
        let indicator = document.querySelector('.mobile-loading-indicator');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.className = 'mobile-loading-indicator';
            indicator.innerHTML = `
                <div class="spinner-border text-light" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
            `;
            indicator.style.cssText = `
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                z-index: 1000;
                background: rgba(0, 0, 0, 0.7);
                padding: 1rem;
                border-radius: 8px;
            `;
            this.video.parentElement.appendChild(indicator);
        }
        indicator.style.display = 'block';
    }

    /**
     * 隐藏加载指示器
     */
    hideLoadingIndicator() {
        const indicator = document.querySelector('.mobile-loading-indicator');
        if (indicator) {
            indicator.style.display = 'none';
        }
    }

    /**
     * 设置触摸事件
     */
    setupTouchEvents() {
        let touchStartTime = 0;
        let touchStartX = 0;
        let touchStartY = 0;

        this.video.addEventListener('touchstart', (e) => {
            touchStartTime = Date.now();
            touchStartX = e.touches[0].clientX;
            touchStartY = e.touches[0].clientY;
        });

        this.video.addEventListener('touchend', (e) => {
            const touchDuration = Date.now() - touchStartTime;
            const touchEndX = e.changedTouches[0].clientX;
            const touchEndY = e.changedTouches[0].clientY;
            
            const deltaX = Math.abs(touchEndX - touchStartX);
            const deltaY = Math.abs(touchEndY - touchStartY);
            
            // 短触摸且没有滑动 - 切换播放/暂停
            if (touchDuration < 300 && deltaX < 10 && deltaY < 10) {
                this.togglePlayPause();
            }
        });

        // 防止双击缩放
        this.video.addEventListener('touchstart', (e) => {
            if (e.touches.length > 1) {
                e.preventDefault();
            }
        });
    }

    /**
     * 切换播放/暂停
     */
    togglePlayPause() {
        if (this.video.paused) {
            this.video.play().catch(err => {
                console.log('播放被阻止:', err);
                this.showPlayButton();
            });
        } else {
            this.video.pause();
        }
    }

    /**
     * 显示播放按钮提示
     */
    showPlayButton() {
        const playButton = document.createElement('div');
        playButton.className = 'mobile-play-button';
        playButton.innerHTML = '<i class="fas fa-play"></i>';
        playButton.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.7);
            color: white;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            cursor: pointer;
            z-index: 1000;
        `;

        playButton.addEventListener('click', () => {
            this.video.play();
            playButton.remove();
        });

        this.video.parentElement.appendChild(playButton);

        // 3秒后自动隐藏
        setTimeout(() => {
            if (playButton.parentElement) {
                playButton.remove();
            }
        }, 3000);
    }

    /**
     * 设置屏幕方向变化处理
     */
    setupOrientationChange() {
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                this.adjustVideoSize();
            }, 100);
        });

        window.addEventListener('resize', () => {
            this.adjustVideoSize();
        });
    }

    /**
     * 调整视频尺寸
     */
    adjustVideoSize() {
        if (!this.isMobile) return;

        const container = this.video.parentElement;
        if (container) {
            // 横屏时调整
            if (window.orientation === 90 || window.orientation === -90) {
                container.style.maxWidth = '100vw';
                container.style.height = '100vh';
            } else {
                container.style.maxWidth = '600px';
                container.style.height = 'auto';
            }
        }
    }

    /**
     * 网络优化
     */
    setupNetworkOptimization() {
        // 检测网络状态
        if ('connection' in navigator) {
            const connection = navigator.connection;
            
            // 根据网络状态调整预加载策略
            if (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g') {
                this.video.preload = 'none';
                console.log('检测到慢速网络，禁用预加载');
            } else if (connection.effectiveType === '3g') {
                this.video.preload = 'metadata';
                console.log('检测到3G网络，使用元数据预加载');
            }

            // 监听网络状态变化
            connection.addEventListener('change', () => {
                console.log('网络状态变化:', connection.effectiveType);
                this.adjustNetworkSettings();
            });
        }
    }

    /**
     * 根据网络状态调整设置
     */
    adjustNetworkSettings() {
        if ('connection' in navigator) {
            const connection = navigator.connection;
            
            if (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g') {
                // 慢速网络：降低质量
                this.video.preload = 'none';
            } else {
                this.video.preload = 'metadata';
            }
        }
    }

    /**
     * 处理OSS URL优化
     */
    static optimizeOSSUrl(url) {
        if (!url || !url.includes('aliyuncs.com')) {
            return url;
        }

        // 为阿里云OSS添加优化参数
        const separator = url.includes('?') ? '&' : '?';
        
        // 移动端优化参数
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        
        if (isMobile) {
            // 移动端使用较低分辨率以节省流量
            return url + separator + 'x-oss-process=video/snapshot,t_1000,f_jpg,w_600,h_400,m_fast';
        } else {
            // 桌面端使用高分辨率
            return url + separator + 'x-oss-process=video/snapshot,t_1000,f_jpg,w_1200,h_800,m_fast';
        }
    }
}

// 自动初始化
document.addEventListener('DOMContentLoaded', function() {
    const videoElement = document.getElementById('video-player');
    if (videoElement) {
        // 优化OSS URL
        const originalSrc = videoElement.src || videoElement.dataset.videoUrl;
        if (originalSrc) {
            const optimizedSrc = MobileVideoPlayer.optimizeOSSUrl(originalSrc);
            videoElement.src = optimizedSrc;
        }

        // 初始化移动端播放器
        new MobileVideoPlayer(videoElement);
    }
});

// 导出类供其他脚本使用
window.MobileVideoPlayer = MobileVideoPlayer;
