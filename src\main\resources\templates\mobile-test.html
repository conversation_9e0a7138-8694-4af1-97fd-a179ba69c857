<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="color-scheme" content="light only">
    <title>移动端视频播放测试</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- 自定义CSS -->
    <link href="/css/style.css" rel="stylesheet">
    <link href="/css/mobile-video.css" rel="stylesheet">
    
    <style>
        .test-section {
            margin: 1rem 0;
            padding: 1rem;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
        .test-result {
            margin-top: 0.5rem;
            padding: 0.5rem;
            border-radius: 4px;
        }
        .test-pass { background-color: #d4edda; color: #155724; }
        .test-fail { background-color: #f8d7da; color: #721c24; }
        .device-info {
            background-color: #e2e3e5;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-12 col-md-8">
                <h1 class="text-center my-4">移动端视频播放测试</h1>
                
                <!-- 设备信息 -->
                <div class="device-info">
                    <h5><i class="fas fa-mobile-alt me-2"></i>设备信息</h5>
                    <div id="device-info-content">
                        <p><strong>用户代理:</strong> <span id="user-agent"></span></p>
                        <p><strong>设备类型:</strong> <span id="device-type"></span></p>
                        <p><strong>屏幕尺寸:</strong> <span id="screen-size"></span></p>
                        <p><strong>网络状态:</strong> <span id="network-status"></span></p>
                    </div>
                </div>

                <!-- 视频播放测试 -->
                <div class="test-section">
                    <h5><i class="fas fa-play-circle me-2"></i>视频播放测试</h5>
                    <div class="video-player-container">
                        <div class="video-wrapper">
                            <video
                                id="test-video"
                                controls
                                preload="metadata"
                                playsinline
                                webkit-playsinline
                                x5-video-player-type="h5"
                                x5-video-player-fullscreen="true"
                                x5-video-orientation="portraint"
                                crossorigin="anonymous"
                                poster="https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/images/jyqk-sos-01.png">
                                <!-- 测试用的视频URL，请替换为您的实际OSS视频URL -->
                                <source src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4" type="video/mp4">
                                您的浏览器不支持HTML5视频播放。
                            </video>
                        </div>
                    </div>
                    <div id="video-test-result" class="test-result"></div>
                </div>

                <!-- 功能测试 -->
                <div class="test-section">
                    <h5><i class="fas fa-cogs me-2"></i>功能测试</h5>
                    <div class="row">
                        <div class="col-6">
                            <button class="btn btn-primary btn-sm w-100 mb-2" onclick="testPlay()">
                                <i class="fas fa-play"></i> 播放测试
                            </button>
                        </div>
                        <div class="col-6">
                            <button class="btn btn-secondary btn-sm w-100 mb-2" onclick="testPause()">
                                <i class="fas fa-pause"></i> 暂停测试
                            </button>
                        </div>
                        <div class="col-6">
                            <button class="btn btn-info btn-sm w-100 mb-2" onclick="testFullscreen()">
                                <i class="fas fa-expand"></i> 全屏测试
                            </button>
                        </div>
                        <div class="col-6">
                            <button class="btn btn-warning btn-sm w-100 mb-2" onclick="testVolume()">
                                <i class="fas fa-volume-up"></i> 音量测试
                            </button>
                        </div>
                    </div>
                    <div id="function-test-result" class="test-result"></div>
                </div>

                <!-- 兼容性测试结果 -->
                <div class="test-section">
                    <h5><i class="fas fa-check-circle me-2"></i>兼容性检查</h5>
                    <div id="compatibility-results"></div>
                </div>

                <!-- 返回按钮 -->
                <div class="text-center my-4">
                    <a href="/" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-2"></i>返回首页
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 设备检测
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
        const isAndroid = /Android/.test(navigator.userAgent);
        
        // 显示设备信息
        document.getElementById('user-agent').textContent = navigator.userAgent;
        document.getElementById('device-type').textContent = isMobile ? 
            (isIOS ? 'iOS设备' : isAndroid ? 'Android设备' : '其他移动设备') : '桌面设备';
        document.getElementById('screen-size').textContent = `${screen.width}x${screen.height}`;
        
        // 网络状态检测
        if ('connection' in navigator) {
            const connection = navigator.connection;
            document.getElementById('network-status').textContent = 
                `${connection.effectiveType || 'unknown'} (${connection.downlink || 'unknown'}Mbps)`;
        } else {
            document.getElementById('network-status').textContent = '无法检测';
        }

        // 视频元素
        const video = document.getElementById('test-video');
        
        // 兼容性检查
        function checkCompatibility() {
            const results = [];
            
            // HTML5 Video 支持
            results.push({
                name: 'HTML5 Video 支持',
                pass: !!document.createElement('video').canPlayType,
                message: document.createElement('video').canPlayType ? '支持' : '不支持'
            });
            
            // MP4 支持
            const canPlayMP4 = document.createElement('video').canPlayType('video/mp4');
            results.push({
                name: 'MP4 格式支持',
                pass: canPlayMP4 !== '',
                message: canPlayMP4 || '不支持'
            });
            
            // 全屏API支持
            results.push({
                name: '全屏API支持',
                pass: !!(document.fullscreenEnabled || document.webkitFullscreenEnabled),
                message: (document.fullscreenEnabled || document.webkitFullscreenEnabled) ? '支持' : '不支持'
            });
            
            // 触摸事件支持
            results.push({
                name: '触摸事件支持',
                pass: 'ontouchstart' in window,
                message: 'ontouchstart' in window ? '支持' : '不支持'
            });
            
            // 显示结果
            const container = document.getElementById('compatibility-results');
            container.innerHTML = results.map(result => 
                `<div class="test-result ${result.pass ? 'test-pass' : 'test-fail'}">
                    <i class="fas fa-${result.pass ? 'check' : 'times'} me-2"></i>
                    <strong>${result.name}:</strong> ${result.message}
                </div>`
            ).join('');
        }
        
        // 测试函数
        function testPlay() {
            video.play().then(() => {
                showResult('function-test-result', true, '播放功能正常');
            }).catch(err => {
                showResult('function-test-result', false, '播放失败: ' + err.message);
            });
        }
        
        function testPause() {
            video.pause();
            showResult('function-test-result', true, '暂停功能正常');
        }
        
        function testFullscreen() {
            if (video.requestFullscreen) {
                video.requestFullscreen().then(() => {
                    showResult('function-test-result', true, '全屏功能正常');
                }).catch(err => {
                    showResult('function-test-result', false, '全屏失败: ' + err.message);
                });
            } else if (video.webkitRequestFullscreen) {
                video.webkitRequestFullscreen();
                showResult('function-test-result', true, '全屏功能正常 (webkit)');
            } else {
                showResult('function-test-result', false, '不支持全屏功能');
            }
        }
        
        function testVolume() {
            const originalVolume = video.volume;
            video.volume = 0.5;
            setTimeout(() => {
                video.volume = originalVolume;
                showResult('function-test-result', true, '音量控制正常');
            }, 1000);
        }
        
        function showResult(elementId, pass, message) {
            const element = document.getElementById(elementId);
            element.className = `test-result ${pass ? 'test-pass' : 'test-fail'}`;
            element.innerHTML = `<i class="fas fa-${pass ? 'check' : 'times'} me-2"></i>${message}`;
        }
        
        // 视频事件监听
        video.addEventListener('loadedmetadata', () => {
            showResult('video-test-result', true, '视频元数据加载成功');
        });
        
        video.addEventListener('error', (e) => {
            showResult('video-test-result', false, '视频加载失败: ' + (video.error ? video.error.message : '未知错误'));
        });
        
        // 页面加载完成后执行检查
        document.addEventListener('DOMContentLoaded', function() {
            checkCompatibility();
            
            // 如果是移动设备，应用移动端优化
            if (isMobile) {
                new MobileVideoPlayer(video);
            }
        });
    </script>
    
    <!-- 移动端优化脚本 -->
    <script src="/js/mobile-video-player.js"></script>
</body>
</html>
