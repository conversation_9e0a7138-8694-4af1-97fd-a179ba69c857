# 移动端视频播放优化指南

## 🎯 优化概述

本项目已针对手机端视频播放进行了全面优化，特别是解决了阿里云OSS视频在移动端无法播放的问题。

## 🔧 已实施的优化措施

### 1. HTML5 Video 标签优化

```html
<video
    id="video-player"
    controls
    preload="metadata"
    playsinline
    webkit-playsinline
    x5-video-player-type="h5"
    x5-video-player-fullscreen="true"
    x5-video-orientation="portraint"
    crossorigin="anonymous">
```

**关键属性说明：**
- `playsinline` - 防止iOS自动全屏播放
- `webkit-playsinline` - iOS Safari兼容
- `x5-video-player-type="h5"` - 微信/QQ浏览器H5播放
- `x5-video-player-fullscreen="true"` - 允许全屏
- `crossorigin="anonymous"` - 解决OSS跨域问题

### 2. JavaScript 移动端检测和优化

新增 `mobile-video-player.js` 文件，包含：

- **设备检测**：自动识别iOS、Android等移动设备
- **触摸优化**：支持触摸播放/暂停控制
- **网络优化**：根据网络状况调整预加载策略
- **URL优化**：为OSS视频添加移动端优化参数

### 3. CSS 响应式设计

新增 `mobile-video.css` 文件，包含：

- **响应式布局**：适配各种屏幕尺寸
- **横屏优化**：横屏时全屏播放
- **触摸优化**：增大触摸目标，防止误操作
- **加载状态**：优化移动端加载提示

### 4. 服务端优化

#### VideoUrlUtil 工具类
```java
// 自动优化OSS URL
String optimizedUrl = VideoUrlUtil.optimizeVideoUrl(videoUrl, isMobile);

// 移动端添加优化参数
if (isMobile) {
    url += "?x-oss-process=video/snapshot,t_1000,f_jpg,w_600,h_400,m_fast";
}
```

#### CORS 配置
```java
// 支持OSS跨域访问
registry.addMapping("/api/videos/**")
        .allowedOriginPatterns("*.aliyuncs.com", "*.alicdn.com")
        .allowedMethods("GET", "HEAD", "OPTIONS")
        .allowCredentials(false);
```

## 🚀 阿里云OSS配置建议

### 1. CORS 设置

在阿里云OSS控制台设置CORS规则：

```xml
<CORSRule>
    <AllowedOrigin>*</AllowedOrigin>
    <AllowedMethod>GET</AllowedMethod>
    <AllowedMethod>HEAD</AllowedMethod>
    <AllowedMethod>OPTIONS</AllowedMethod>
    <AllowedHeader>*</AllowedHeader>
    <MaxAgeSeconds>3600</MaxAgeSeconds>
</CORSRule>
```

### 2. 防盗链设置

建议设置合理的防盗链规则，允许您的域名访问：

```
允许的Referer：
- https://yourdomain.com/*
- https://*.yourdomain.com/*
- 空Referer（移动端APP访问）
```

### 3. 视频处理

OSS支持实时视频处理，可以：
- 生成不同分辨率的视频
- 自动生成缩略图
- 添加水印等

## 📱 移动端兼容性

### 支持的设备和浏览器

✅ **iOS设备**
- Safari (iOS 10+)
- Chrome (iOS)
- 微信内置浏览器
- QQ浏览器

✅ **Android设备**
- Chrome (Android 5.0+)
- 微信内置浏览器
- QQ浏览器
- UC浏览器
- 华为浏览器

✅ **特殊优化**
- 微信小程序 WebView
- QQ浏览器 X5 内核
- 各厂商定制浏览器

### 已解决的常见问题

1. **iOS Safari 自动全屏** ✅
   - 使用 `playsinline` 属性解决

2. **Android 微信无法播放** ✅
   - 使用 `x5-video-player-type="h5"` 解决

3. **OSS 跨域问题** ✅
   - 配置 CORS 和 `crossorigin` 属性

4. **移动端控制困难** ✅
   - 添加触摸手势支持

5. **网络慢加载问题** ✅
   - 智能预加载策略

## 🔍 故障排除

### 常见错误及解决方案

#### 1. "视频格式不支持或文件损坏" 错误

**原因分析：**
- 浏览器不支持当前视频编码格式
- 视频文件本身有问题
- OSS处理参数不正确

**解决方案：**
```javascript
// 检查浏览器格式支持
const video = document.createElement('video');
console.log('MP4支持:', video.canPlayType('video/mp4; codecs="avc1.42E01E, mp4a.40.2"'));
console.log('WebM支持:', video.canPlayType('video/webm; codecs="vp8, vorbis"'));
```

**推荐设置：**
- 使用 H.264 编码的 MP4 格式
- 音频使用 AAC 编码
- 分辨率不超过 1080p
- 码率控制在 2Mbps 以内

#### 2. 移动端无法播放

**iOS设备：**
```html
<!-- 必需属性 -->
<video playsinline webkit-playsinline controls>
```

**Android设备：**
```html
<!-- 微信/QQ浏览器优化 -->
<video x5-video-player-type="h5" x5-video-player-fullscreen="true">
```

#### 3. OSS跨域问题

**CORS配置：**
```xml
<CORSRule>
    <AllowedOrigin>*</AllowedOrigin>
    <AllowedMethod>GET</AllowedMethod>
    <AllowedMethod>HEAD</AllowedMethod>
    <AllowedMethod>OPTIONS</AllowedMethod>
    <AllowedHeader>*</AllowedHeader>
    <MaxAgeSeconds>3600</MaxAgeSeconds>
</CORSRule>
```

**测试命令：**
```bash
# 测试OSS URL是否可访问
curl -I "https://your-bucket.oss-cn-hangzhou.aliyuncs.com/video.mp4"

# 测试CORS
curl -H "Origin: https://yourdomain.com" \
     -H "Access-Control-Request-Method: GET" \
     -H "Access-Control-Request-Headers: X-Requested-With" \
     -X OPTIONS \
     "https://your-bucket.oss-cn-hangzhou.aliyuncs.com/video.mp4"
```

#### 4. 网络相关问题

**移动网络限制：**
- 某些运营商限制视频流量
- 建议在WiFi环境下测试
- 使用低码率版本

**CDN优化：**
```javascript
// 根据网络状况选择不同质量
if (navigator.connection) {
    const connection = navigator.connection;
    if (connection.effectiveType === '2g') {
        // 使用低质量版本
        videoUrl += '?x-oss-process=video/convert,f_mp4,vb_500k,s_480x360';
    }
}
```

### 调试工具

在浏览器控制台查看详细日志：

```javascript
// 查看设备信息
console.log('设备类型:', isMobile ? '移动设备' : '桌面设备');

// 查看视频错误
video.addEventListener('error', function(e) {
    console.error('视频错误:', e);
    console.error('错误代码:', video.error.code);
});
```

## 📊 性能优化建议

### 1. 视频文件优化
- 使用适合移动端的分辨率（720p或以下）
- 控制码率（建议1-3Mbps）
- 使用现代编码格式

### 2. CDN加速
- 启用阿里云CDN
- 配置智能压缩
- 设置合理的缓存策略

### 3. 预加载策略
```javascript
// 根据网络状况调整
if (connection.effectiveType === '2g') {
    video.preload = 'none';
} else {
    video.preload = 'metadata';
}
```

## 🎉 更新日志

### v1.0.0 (当前版本)
- ✅ 完整的移动端视频播放支持
- ✅ 阿里云OSS跨域问题解决
- ✅ 响应式设计和触摸优化
- ✅ 智能网络适配
- ✅ 多浏览器兼容性

---

如有问题，请查看浏览器控制台的错误信息，或联系技术支持。
