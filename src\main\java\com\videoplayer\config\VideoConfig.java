package com.videoplayer.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 视频相关配置类
 * 处理视频播放、OSS访问等配置
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Configuration
public class VideoConfig implements WebMvcConfigurer {

    /**
     * 配置CORS以支持OSS视频播放
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD")
                .allowedHeaders("*")
                .allowCredentials(true)
                .maxAge(3600);
                
        // 特别为阿里云OSS配置CORS
        registry.addMapping("/api/videos/**")
                .allowedOriginPatterns("*.aliyuncs.com", "*.alicdn.com")
                .allowedMethods("GET", "HEAD", "OPTIONS")
                .allowedHeaders("*")
                .allowCredentials(false)
                .maxAge(86400); // 24小时缓存
    }
}
