-- 视频播放器数据库初始化脚本
-- 数据库版本: MySQL 8.0+
-- 字符集: UTF-8

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `video_player` 
DEFAULT CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE `video_player`;

-- 创建视频表
CREATE TABLE IF NOT EXISTS `videos` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '视频ID',
    `title` VARCHAR(200) NOT NULL COMMENT '视频标题',
    `description` VARCHAR(500) DEFAULT NULL COMMENT '视频描述',
    `video_url` VARCHAR(1000) NOT NULL COMMENT '视频URL地址',
    `thumbnail_url` VARCHAR(1000) DEFAULT NULL COMMENT '缩略图URL',
    `duration` INT DEFAULT NULL COMMENT '视频时长（秒）',
    `file_size` BIGINT DEFAULT NULL COMMENT '文件大小（字节）',
    `video_format` VARCHAR(20) DEFAULT NULL COMMENT '视频格式',
    `resolution` VARCHAR(20) DEFAULT NULL COMMENT '分辨率',
    `is_active` BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX `idx_title` (`title`),
    INDEX `idx_is_active` (`is_active`),
    INDEX `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频信息表';

-- 插入示例数据（阿里云OSS视频链接示例）
INSERT INTO `videos` (`title`, `description`, `video_url`, `thumbnail_url`, `duration`, `video_format`, `resolution`) VALUES
('移动端测试视频', '专门用于测试移动端播放的视频，已优化CORS和格式兼容性', 'https://your-bucket.oss-cn-hangzhou.aliyuncs.com/videos/mobile-test.mp4', 'https://your-bucket.oss-cn-hangzhou.aliyuncs.com/thumbnails/mobile-test.jpg', 120, 'mp4', '720p'),
('高清示例视频', '高清视频播放测试，支持桌面端和移动端', 'https://your-bucket.oss-cn-hangzhou.aliyuncs.com/videos/hd-sample.mp4', 'https://your-bucket.oss-cn-hangzhou.aliyuncs.com/thumbnails/hd-sample.jpg', 180, 'mp4', '1080p'),
('兼容性测试视频', '用于测试各种浏览器和设备的兼容性', 'https://your-bucket.oss-cn-hangzhou.aliyuncs.com/videos/compatibility-test.mp4', 'https://your-bucket.oss-cn-hangzhou.aliyuncs.com/thumbnails/compatibility-test.jpg', 60, 'mp4', '480p'),
('网络优化视频', '针对移动网络优化的低码率视频', 'https://your-bucket.oss-cn-hangzhou.aliyuncs.com/videos/network-optimized.mp4', 'https://your-bucket.oss-cn-hangzhou.aliyuncs.com/thumbnails/network-optimized.jpg', 90, 'mp4', '360p');



-- 显示创建结果
SHOW TABLES;
SELECT COUNT(*) as video_count FROM videos;

-- 数据库初始化完成
SELECT '数据库初始化完成！' as message;

