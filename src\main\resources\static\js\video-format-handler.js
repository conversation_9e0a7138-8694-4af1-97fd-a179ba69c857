/**
 * 视频格式处理和兼容性检测
 * Video Format Handler and Compatibility Detection
 * <AUTHOR>
 * @version 1.0.0
 */

class VideoFormatHandler {
    constructor() {
        this.supportedFormats = this.detectSupportedFormats();
        this.deviceInfo = this.getDeviceInfo();
    }

    /**
     * 检测浏览器支持的视频格式
     */
    detectSupportedFormats() {
        const video = document.createElement('video');
        const formats = {
            mp4: {
                basic: video.canPlayType('video/mp4'),
                h264: video.canPlayType('video/mp4; codecs="avc1.42E01E, mp4a.40.2"'),
                h264_high: video.canPlayType('video/mp4; codecs="avc1.64001E, mp4a.40.2"')
            },
            webm: {
                basic: video.canPlayType('video/webm'),
                vp8: video.canPlayType('video/webm; codecs="vp8, vorbis"'),
                vp9: video.canPlayType('video/webm; codecs="vp9, opus"')
            },
            ogg: {
                basic: video.canPlayType('video/ogg'),
                theora: video.canPlayType('video/ogg; codecs="theora, vorbis"')
            },
            hls: {
                basic: video.canPlayType('application/vnd.apple.mpegurl'),
                m3u8: video.canPlayType('application/x-mpegURL')
            }
        };

        console.log('浏览器支持的视频格式:', formats);
        return formats;
    }

    /**
     * 获取设备信息
     */
    getDeviceInfo() {
        const userAgent = navigator.userAgent;
        return {
            isMobile: /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent),
            isIOS: /iPad|iPhone|iPod/.test(userAgent),
            isAndroid: /Android/.test(userAgent),
            isWeChat: /MicroMessenger/i.test(userAgent),
            isQQ: /QQBrowser/i.test(userAgent),
            isSafari: /Safari/.test(userAgent) && !/Chrome/.test(userAgent),
            isChrome: /Chrome/.test(userAgent),
            isFirefox: /Firefox/.test(userAgent)
        };
    }

    /**
     * 获取最佳视频格式
     */
    getBestFormat(videoUrl) {
        const device = this.deviceInfo;
        const formats = this.supportedFormats;

        // iOS设备优先使用MP4
        if (device.isIOS) {
            if (formats.mp4.h264 === 'probably' || formats.mp4.h264 === 'maybe') {
                return this.generateMP4Url(videoUrl, 'h264');
            }
            return this.generateMP4Url(videoUrl, 'basic');
        }

        // Android设备
        if (device.isAndroid) {
            // 微信浏览器特殊处理
            if (device.isWeChat) {
                return this.generateMP4Url(videoUrl, 'h264', true);
            }
            
            // 其他Android浏览器
            if (formats.mp4.h264 === 'probably') {
                return this.generateMP4Url(videoUrl, 'h264');
            }
            return this.generateMP4Url(videoUrl, 'basic');
        }

        // 桌面浏览器
        if (formats.mp4.h264 === 'probably') {
            return this.generateMP4Url(videoUrl, 'h264');
        } else if (formats.webm.vp9 === 'probably') {
            return this.generateWebMUrl(videoUrl, 'vp9');
        } else if (formats.webm.vp8 === 'probably') {
            return this.generateWebMUrl(videoUrl, 'vp8');
        }

        return videoUrl; // 返回原始URL作为兜底
    }

    /**
     * 生成MP4格式URL
     */
    generateMP4Url(originalUrl, codec = 'basic', isWeChat = false) {
        if (!originalUrl.includes('aliyuncs.com')) {
            return originalUrl;
        }

        const separator = originalUrl.includes('?') ? '&' : '?';
        let processParams = '';

        if (codec === 'h264') {
            if (this.deviceInfo.isMobile) {
                // 移动端使用较低码率
                processParams = 'x-oss-process=video/convert,f_mp4,vcodec_h264,acodec_aac,vb_800k,ab_128k,s_720x480';
                
                if (isWeChat) {
                    // 微信浏览器进一步优化
                    processParams = 'x-oss-process=video/convert,f_mp4,vcodec_h264,acodec_aac,vb_600k,ab_96k,s_640x360';
                }
            } else {
                // 桌面端使用高码率
                processParams = 'x-oss-process=video/convert,f_mp4,vcodec_h264,acodec_aac,vb_2000k,ab_192k';
            }
        } else {
            // 基础MP4格式
            if (this.deviceInfo.isMobile) {
                processParams = 'x-oss-process=video/convert,f_mp4,s_720x480';
            } else {
                processParams = 'x-oss-process=video/convert,f_mp4';
            }
        }

        return originalUrl + separator + processParams;
    }

    /**
     * 生成WebM格式URL
     */
    generateWebMUrl(originalUrl, codec = 'vp8') {
        if (!originalUrl.includes('aliyuncs.com')) {
            return originalUrl;
        }

        const separator = originalUrl.includes('?') ? '&' : '?';
        let processParams = '';

        if (codec === 'vp9') {
            processParams = 'x-oss-process=video/convert,f_webm,vcodec_vp9,acodec_opus';
        } else {
            processParams = 'x-oss-process=video/convert,f_webm,vcodec_vp8,acodec_vorbis';
        }

        return originalUrl + separator + processParams;
    }

    /**
     * 创建多源视频元素
     */
    createVideoSources(videoElement, originalUrl) {
        // 清除现有源
        const existingSources = videoElement.querySelectorAll('source');
        existingSources.forEach(source => source.remove());

        const formats = this.supportedFormats;
        const sources = [];

        // 根据支持情况添加源
        if (formats.mp4.h264 === 'probably' || formats.mp4.h264 === 'maybe') {
            sources.push({
                src: this.generateMP4Url(originalUrl, 'h264'),
                type: 'video/mp4; codecs="avc1.42E01E, mp4a.40.2"'
            });
        }

        if (formats.mp4.basic === 'probably' || formats.mp4.basic === 'maybe') {
            sources.push({
                src: this.generateMP4Url(originalUrl, 'basic'),
                type: 'video/mp4'
            });
        }

        if (formats.webm.vp9 === 'probably' && !this.deviceInfo.isMobile) {
            sources.push({
                src: this.generateWebMUrl(originalUrl, 'vp9'),
                type: 'video/webm; codecs="vp9, opus"'
            });
        }

        if (formats.webm.vp8 === 'probably' && !this.deviceInfo.isMobile) {
            sources.push({
                src: this.generateWebMUrl(originalUrl, 'vp8'),
                type: 'video/webm; codecs="vp8, vorbis"'
            });
        }

        // 添加源到视频元素
        sources.forEach(sourceData => {
            const source = document.createElement('source');
            source.src = sourceData.src;
            source.type = sourceData.type;
            videoElement.appendChild(source);
        });

        // 添加兜底源
        const fallbackSource = document.createElement('source');
        fallbackSource.src = originalUrl;
        fallbackSource.type = 'video/mp4';
        videoElement.appendChild(fallbackSource);

        return sources;
    }

    /**
     * 检查视频是否可以播放
     */
    async checkVideoPlayability(videoUrl) {
        return new Promise((resolve) => {
            const testVideo = document.createElement('video');
            testVideo.style.display = 'none';
            testVideo.muted = true;
            testVideo.preload = 'metadata';

            const timeout = setTimeout(() => {
                document.body.removeChild(testVideo);
                resolve(false);
            }, 10000); // 10秒超时

            testVideo.addEventListener('loadedmetadata', () => {
                clearTimeout(timeout);
                document.body.removeChild(testVideo);
                resolve(true);
            });

            testVideo.addEventListener('error', () => {
                clearTimeout(timeout);
                document.body.removeChild(testVideo);
                resolve(false);
            });

            testVideo.src = videoUrl;
            document.body.appendChild(testVideo);
        });
    }

    /**
     * 获取格式兼容性报告
     */
    getCompatibilityReport() {
        const formats = this.supportedFormats;
        const device = this.deviceInfo;

        const report = {
            device: device,
            recommendations: [],
            warnings: [],
            supported: {
                mp4: formats.mp4.basic !== '',
                webm: formats.webm.basic !== '',
                ogg: formats.ogg.basic !== ''
            }
        };

        // 生成建议
        if (device.isMobile) {
            if (!formats.mp4.h264 || formats.mp4.h264 === '') {
                report.warnings.push('您的移动浏览器对H.264编码支持有限');
                report.recommendations.push('建议使用Chrome或Safari浏览器');
            }

            if (device.isWeChat) {
                report.recommendations.push('微信浏览器已优化，使用低码率视频');
            }
        }

        if (!report.supported.mp4) {
            report.warnings.push('您的浏览器不支持MP4格式');
            report.recommendations.push('请更新浏览器到最新版本');
        }

        return report;
    }

    /**
     * 显示兼容性警告
     */
    showCompatibilityWarning(container, report) {
        if (report.warnings.length === 0) return;

        const warningDiv = document.createElement('div');
        warningDiv.className = 'alert alert-warning mt-2';
        warningDiv.innerHTML = `
            <h6><i class="fas fa-exclamation-triangle"></i> 兼容性提示</h6>
            <div class="mb-2">
                ${report.warnings.map(warning => `<p class="mb-1">• ${warning}</p>`).join('')}
            </div>
            ${report.recommendations.length > 0 ? `
                <div>
                    <strong>建议：</strong>
                    ${report.recommendations.map(rec => `<p class="mb-1">• ${rec}</p>`).join('')}
                </div>
            ` : ''}
        `;

        if (container && !container.querySelector('.alert-warning')) {
            container.appendChild(warningDiv);
        }
    }
}

// 自动初始化
window.VideoFormatHandler = VideoFormatHandler;
