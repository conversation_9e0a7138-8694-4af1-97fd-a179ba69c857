/**
 * 移动端视频播放器专用样式
 * Mobile Video Player Styles
 * <AUTHOR>
 * @version 1.0.0
 */

/* 移动端视频播放器基础样式 */
@media screen and (max-width: 768px) {
    /* 视频播放器容器 */
    .video-player-container {
        margin: 0 !important;
        border-radius: 0 !important;
        max-width: 100% !important;
        width: 100% !important;
    }

    /* 视频包装器 */
    .video-wrapper {
        border-radius: 0 !important;
        position: relative;
        width: 100%;
        padding-bottom: 56.25%; /* 16:9 宽高比 */
        background: #000;
        overflow: hidden;
    }

    /* 视频元素 */
    #video-player {
        position: absolute;
        top: 0;
        left: 0;
        width: 100% !important;
        height: 100% !important;
        border-radius: 0 !important;
        object-fit: contain;
        background: #000;
    }

    /* 移动端播放按钮 */
    .mobile-play-button {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.7);
        color: white;
        border-radius: 50%;
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        cursor: pointer;
        z-index: 1000;
        transition: all 0.3s ease;
    }

    .mobile-play-button:hover {
        background: rgba(0, 0, 0, 0.9);
        transform: translate(-50%, -50%) scale(1.1);
    }

    /* 视频信息区域移动端优化 */
    .video-info {
        margin: 0 !important;
        border-radius: 0 !important;
        padding: 1rem !important;
        box-shadow: none !important;
        border-top: 1px solid #e9ecef;
    }

    /* 视频标题移动端优化 */
    .video-title {
        font-size: 1.2rem !important;
        line-height: 1.4;
        margin-bottom: 0.5rem !important;
        text-align: left !important;
    }

    /* 视频描述移动端优化 */
    .video-description {
        font-size: 0.9rem;
        line-height: 1.5;
        color: #666;
    }

    /* 视频统计信息 */
    .video-stats {
        font-size: 0.85rem;
        color: #888;
    }
}

/* 横屏模式优化 */
@media screen and (max-width: 768px) and (orientation: landscape) {
    .video-player-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        z-index: 9999;
        background: #000;
    }

    .video-wrapper {
        padding-bottom: 0;
        height: 100vh;
    }

    #video-player {
        width: 100vw;
        height: 100vh;
        object-fit: contain;
    }

    /* 隐藏其他内容 */
    .video-info,
    .navbar,
    .container-fluid > .row > .col-12:not(:first-child) {
        display: none !important;
    }
}

/* iOS Safari 特殊优化 */
@supports (-webkit-appearance: none) {
    @media screen and (max-width: 768px) {
        #video-player {
            -webkit-playsinline: true;
            playsinline: true;
        }

        /* iOS 全屏播放优化 */
        #video-player::-webkit-media-controls-fullscreen-button {
            display: none;
        }

        /* iOS 播放控制优化 */
        #video-player::-webkit-media-controls {
            background: rgba(0, 0, 0, 0.5);
        }
    }
}

/* Android 浏览器优化 */
@media screen and (max-width: 768px) {
    /* Android Chrome 优化 */
    .video-wrapper video[x5-video-player-type="h5"] {
        object-position: center;
    }

    /* 微信浏览器优化 */
    .video-wrapper video[x5-video-orientation="portraint"] {
        object-fit: contain;
    }
}

/* 加载状态移动端优化 */
@media screen and (max-width: 768px) {
    .video-loading {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1001;
        background: rgba(0, 0, 0, 0.7);
        border-radius: 8px;
        padding: 1rem;
        color: white;
    }

    .video-loading .spinner-border {
        width: 2rem;
        height: 2rem;
    }
}

/* 错误提示移动端优化 */
@media screen and (max-width: 768px) {
    .alert {
        margin: 0.5rem !important;
        border-radius: 8px !important;
        font-size: 0.9rem;
    }

    .alert h6 {
        font-size: 1rem;
        margin-bottom: 0.5rem;
    }

    .alert ul {
        font-size: 0.85rem;
        margin-bottom: 0.5rem;
    }

    .alert .btn {
        font-size: 0.8rem;
        padding: 0.375rem 0.75rem;
    }
}

/* 触摸优化 */
@media screen and (max-width: 768px) {
    /* 增大触摸目标 */
    #video-player {
        touch-action: manipulation;
    }

    /* 防止双击缩放 */
    .video-wrapper {
        touch-action: manipulation;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        user-select: none;
    }

    /* 播放控制按钮触摸优化 */
    video::-webkit-media-controls-play-button,
    video::-webkit-media-controls-start-playback-button {
        min-width: 44px;
        min-height: 44px;
    }
}

/* 网络状态指示器 */
@media screen and (max-width: 768px) {
    .network-status {
        position: absolute;
        top: 10px;
        right: 10px;
        background: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.75rem;
        z-index: 1002;
    }

    .network-status.slow {
        background: rgba(255, 193, 7, 0.9);
        color: #000;
    }

    .network-status.fast {
        background: rgba(40, 167, 69, 0.9);
    }
}

/* 缓冲进度条 */
@media screen and (max-width: 768px) {
    .buffer-progress {
        position: absolute;
        bottom: 0;
        left: 0;
        height: 3px;
        background: rgba(255, 255, 255, 0.3);
        width: 100%;
        z-index: 1001;
    }

    .buffer-progress-bar {
        height: 100%;
        background: rgba(255, 255, 255, 0.7);
        width: 0%;
        transition: width 0.3s ease;
    }
}

/* 小屏幕设备特殊优化 */
@media screen and (max-width: 480px) {
    .video-title {
        font-size: 1.1rem !important;
    }

    .video-info {
        padding: 0.75rem !important;
    }

    .mobile-play-button {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
}

/* 超小屏幕设备优化 */
@media screen and (max-width: 360px) {
    .video-title {
        font-size: 1rem !important;
    }

    .video-description {
        font-size: 0.85rem;
    }

    .alert {
        font-size: 0.8rem;
    }
}
