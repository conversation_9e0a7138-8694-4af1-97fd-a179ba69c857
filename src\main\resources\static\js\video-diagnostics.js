/**
 * 视频播放诊断工具
 * Video Playback Diagnostics Tool
 * <AUTHOR>
 * @version 1.0.0
 */

class VideoDiagnostics {
    constructor() {
        this.results = {
            device: {},
            network: {},
            browser: {},
            video: {},
            recommendations: []
        };
    }

    /**
     * 运行完整诊断
     */
    async runFullDiagnostics(videoUrl = null) {
        console.log('🔍 开始视频播放诊断...');
        
        this.detectDevice();
        this.detectBrowser();
        this.detectNetwork();
        await this.testVideoFormats();
        
        if (videoUrl) {
            await this.testSpecificVideo(videoUrl);
        }
        
        this.generateRecommendations();
        
        console.log('✅ 诊断完成:', this.results);
        return this.results;
    }

    /**
     * 设备检测
     */
    detectDevice() {
        const userAgent = navigator.userAgent;
        
        this.results.device = {
            isMobile: /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent),
            isIOS: /iPad|iPhone|iPod/.test(userAgent),
            isAndroid: /Android/.test(userAgent),
            isWeChat: /MicroMessenger/i.test(userAgent),
            isQQ: /QQBrowser/i.test(userAgent),
            screen: {
                width: screen.width,
                height: screen.height,
                ratio: screen.width / screen.height
            },
            orientation: screen.orientation ? screen.orientation.type : 'unknown',
            userAgent: userAgent
        };

        // iOS版本检测
        if (this.results.device.isIOS) {
            const match = userAgent.match(/OS (\d+)_(\d+)_?(\d+)?/);
            if (match) {
                this.results.device.iosVersion = parseInt(match[1], 10);
            }
        }

        // Android版本检测
        if (this.results.device.isAndroid) {
            const match = userAgent.match(/Android (\d+)\.(\d+)/);
            if (match) {
                this.results.device.androidVersion = parseFloat(match[1] + '.' + match[2]);
            }
        }
    }

    /**
     * 浏览器检测
     */
    detectBrowser() {
        const userAgent = navigator.userAgent;
        
        this.results.browser = {
            name: this.getBrowserName(userAgent),
            version: this.getBrowserVersion(userAgent),
            engine: this.getBrowserEngine(userAgent),
            supportsHTML5Video: !!document.createElement('video').canPlayType,
            supportsFullscreen: !!(document.fullscreenEnabled || document.webkitFullscreenEnabled),
            supportsTouchEvents: 'ontouchstart' in window,
            cookieEnabled: navigator.cookieEnabled,
            javaEnabled: navigator.javaEnabled ? navigator.javaEnabled() : false
        };

        // 视频格式支持检测
        const video = document.createElement('video');
        this.results.browser.videoFormats = {
            mp4: {
                basic: video.canPlayType('video/mp4'),
                h264: video.canPlayType('video/mp4; codecs="avc1.42E01E, mp4a.40.2"'),
                h264_high: video.canPlayType('video/mp4; codecs="avc1.64001E, mp4a.40.2"')
            },
            webm: {
                basic: video.canPlayType('video/webm'),
                vp8: video.canPlayType('video/webm; codecs="vp8, vorbis"'),
                vp9: video.canPlayType('video/webm; codecs="vp9, opus"')
            },
            ogg: {
                basic: video.canPlayType('video/ogg'),
                theora: video.canPlayType('video/ogg; codecs="theora, vorbis"')
            }
        };
    }

    /**
     * 网络检测
     */
    detectNetwork() {
        this.results.network = {
            online: navigator.onLine,
            connection: null,
            speed: 'unknown'
        };

        // 网络连接信息
        if ('connection' in navigator) {
            const conn = navigator.connection;
            this.results.network.connection = {
                effectiveType: conn.effectiveType,
                downlink: conn.downlink,
                rtt: conn.rtt,
                saveData: conn.saveData
            };
        }

        // 简单的网络速度测试
        this.testNetworkSpeed();
    }

    /**
     * 测试网络速度
     */
    async testNetworkSpeed() {
        try {
            const startTime = Date.now();
            const response = await fetch('/favicon.ico?' + Math.random(), { 
                cache: 'no-cache' 
            });
            const endTime = Date.now();
            
            if (response.ok) {
                const duration = endTime - startTime;
                this.results.network.latency = duration;
                
                if (duration < 100) {
                    this.results.network.speed = 'fast';
                } else if (duration < 300) {
                    this.results.network.speed = 'medium';
                } else {
                    this.results.network.speed = 'slow';
                }
            }
        } catch (error) {
            this.results.network.speed = 'error';
            this.results.network.error = error.message;
        }
    }

    /**
     * 测试视频格式支持
     */
    async testVideoFormats() {
        const testUrls = {
            mp4: 'data:video/mp4;base64,AAAAIGZ0eXBpc29tAAACAGlzb21pc28yYXZjMW1wNDEAAAAIZnJlZQAAAr1tZGF0',
            webm: 'data:video/webm;base64,GkXfo0AgQoaBAUL3gQFC8oEEQvOBCEKCQAR3ZWJtQoeBAkKFgQIYU4BnQI0VSalmQCgq',
            ogg: 'data:video/ogg;base64,T2dnUwACAAAAAAAAAABK/wAAAAAAAM7gds4BHgF2b3JiaXMAAAAAAUSsAAAAAAAAgLsAAAAAAAC4AU9nZ1MAAAAAAAAAAAAA'
        };

        this.results.video.formatTests = {};

        for (const [format, dataUrl] of Object.entries(testUrls)) {
            try {
                const canPlay = await this.testVideoUrl(dataUrl);
                this.results.video.formatTests[format] = canPlay;
            } catch (error) {
                this.results.video.formatTests[format] = false;
            }
        }
    }

    /**
     * 测试特定视频URL
     */
    async testSpecificVideo(videoUrl) {
        try {
            this.results.video.testUrl = videoUrl;
            this.results.video.canPlay = await this.testVideoUrl(videoUrl);
            
            // 测试不同的OSS处理参数
            if (videoUrl.includes('aliyuncs.com')) {
                await this.testOSSVariants(videoUrl);
            }
        } catch (error) {
            this.results.video.error = error.message;
            this.results.video.canPlay = false;
        }
    }

    /**
     * 测试OSS不同处理参数
     */
    async testOSSVariants(baseUrl) {
        const variants = {
            original: baseUrl,
            mobile: baseUrl + (baseUrl.includes('?') ? '&' : '?') + 'x-oss-process=video/convert,f_mp4,vb_800k,s_720x480',
            low: baseUrl + (baseUrl.includes('?') ? '&' : '?') + 'x-oss-process=video/convert,f_mp4,vb_500k,s_480x360'
        };

        this.results.video.ossVariants = {};

        for (const [variant, url] of Object.entries(variants)) {
            try {
                const canPlay = await this.testVideoUrl(url, 5000); // 5秒超时
                this.results.video.ossVariants[variant] = canPlay;
            } catch (error) {
                this.results.video.ossVariants[variant] = false;
            }
        }
    }

    /**
     * 测试视频URL是否可播放
     */
    testVideoUrl(url, timeout = 10000) {
        return new Promise((resolve) => {
            const video = document.createElement('video');
            video.style.display = 'none';
            video.muted = true;
            video.preload = 'metadata';

            const timer = setTimeout(() => {
                cleanup();
                resolve(false);
            }, timeout);

            const cleanup = () => {
                clearTimeout(timer);
                if (video.parentNode) {
                    video.parentNode.removeChild(video);
                }
            };

            video.addEventListener('loadedmetadata', () => {
                cleanup();
                resolve(true);
            });

            video.addEventListener('error', () => {
                cleanup();
                resolve(false);
            });

            video.src = url;
            document.body.appendChild(video);
        });
    }

    /**
     * 生成建议
     */
    generateRecommendations() {
        const recommendations = [];
        const device = this.results.device;
        const browser = this.results.browser;
        const network = this.results.network;
        const video = this.results.video;

        // 设备相关建议
        if (device.isMobile) {
            if (device.isIOS && device.iosVersion < 10) {
                recommendations.push({
                    type: 'warning',
                    message: 'iOS版本较旧，建议更新系统以获得更好的视频播放支持'
                });
            }

            if (device.isWeChat) {
                recommendations.push({
                    type: 'info',
                    message: '微信浏览器环境，已自动优化视频播放参数'
                });
            }
        }

        // 浏览器相关建议
        if (!browser.supportsHTML5Video) {
            recommendations.push({
                type: 'error',
                message: '浏览器不支持HTML5视频，请更新浏览器'
            });
        }

        if (!browser.videoFormats.mp4.h264 || browser.videoFormats.mp4.h264 === '') {
            recommendations.push({
                type: 'warning',
                message: '浏览器对H.264编码支持有限，可能影响视频播放'
            });
        }

        // 网络相关建议
        if (!network.online) {
            recommendations.push({
                type: 'error',
                message: '网络连接异常，请检查网络设置'
            });
        }

        if (network.speed === 'slow') {
            recommendations.push({
                type: 'warning',
                message: '网络速度较慢，建议使用WiFi或选择低清晰度视频'
            });
        }

        if (network.connection && network.connection.saveData) {
            recommendations.push({
                type: 'info',
                message: '检测到数据节省模式，将自动选择低码率视频'
            });
        }

        // 视频相关建议
        if (video.canPlay === false) {
            recommendations.push({
                type: 'error',
                message: '视频无法播放，请检查视频URL或格式'
            });
        }

        this.results.recommendations = recommendations;
    }

    /**
     * 获取浏览器名称
     */
    getBrowserName(userAgent) {
        if (userAgent.includes('Chrome')) return 'Chrome';
        if (userAgent.includes('Firefox')) return 'Firefox';
        if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) return 'Safari';
        if (userAgent.includes('Edge')) return 'Edge';
        if (userAgent.includes('Opera')) return 'Opera';
        return 'Unknown';
    }

    /**
     * 获取浏览器版本
     */
    getBrowserVersion(userAgent) {
        const patterns = {
            Chrome: /Chrome\/(\d+)/,
            Firefox: /Firefox\/(\d+)/,
            Safari: /Version\/(\d+)/,
            Edge: /Edge\/(\d+)/,
            Opera: /Opera\/(\d+)/
        };

        const browserName = this.getBrowserName(userAgent);
        const pattern = patterns[browserName];
        
        if (pattern) {
            const match = userAgent.match(pattern);
            return match ? match[1] : 'Unknown';
        }
        
        return 'Unknown';
    }

    /**
     * 获取浏览器引擎
     */
    getBrowserEngine(userAgent) {
        if (userAgent.includes('WebKit')) return 'WebKit';
        if (userAgent.includes('Gecko')) return 'Gecko';
        if (userAgent.includes('Trident')) return 'Trident';
        return 'Unknown';
    }

    /**
     * 生成诊断报告
     */
    generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                overall: this.getOverallStatus(),
                criticalIssues: this.results.recommendations.filter(r => r.type === 'error').length,
                warnings: this.results.recommendations.filter(r => r.type === 'warning').length
            },
            details: this.results
        };

        return report;
    }

    /**
     * 获取总体状态
     */
    getOverallStatus() {
        const hasErrors = this.results.recommendations.some(r => r.type === 'error');
        const hasWarnings = this.results.recommendations.some(r => r.type === 'warning');

        if (hasErrors) return 'error';
        if (hasWarnings) return 'warning';
        return 'good';
    }
}

// 导出类
window.VideoDiagnostics = VideoDiagnostics;
